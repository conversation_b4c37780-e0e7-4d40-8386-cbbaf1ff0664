from datetime import datetime, UTC
from typing import List, Optional, Set
from uuid import UUID, uuid4
import logging
import os
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from fastapi_pagination import Page, Params
from fastapi_pagination.ext.sqlalchemy import paginate as sqlalchemy_paginate
from pydantic import BaseModel
from sqlalchemy import or_, func
from app.utils.memory import get_memory_client
from app.models import memory_categories
from app.mcp_server import validate_text_length, chunk_text, get_max_text_length_from_config

from app.database import get_db
from app.models import (
    Memory, MemoryState, MemoryAccessLog, App,
    MemoryStatusHistory, User, Category, AccessControl, Config as ConfigModel
)
from app.schemas import MemoryResponse, PaginatedMemoryResponse
from app.utils.permissions import check_memory_access_permissions
from app.config import USER_ID
from app.auth.middleware import get_current_user, Authenticated<PERSON><PERSON>, Default<PERSON><PERSON>
from typing import Union
from fastapi.responses import JSONResponse

router = APIRouter(prefix="/api/v1/memories", tags=["memories"])


def get_memory_or_404(db: Session, memory_id: UUID) -> Memory:
    memory = db.query(Memory).filter(Memory.id == memory_id).first()
    if not memory:
        raise HTTPException(status_code=404, detail="Memory not found")
    return memory


def update_memory_state(db: Session, memory_id: UUID, new_state: MemoryState, user_id: UUID):
    memory = get_memory_or_404(db, memory_id)
    old_state = memory.state

    # Update memory state
    memory.state = new_state.value
    if new_state == MemoryState.archived.value:
        memory.archived_at = datetime.now(UTC)
    elif new_state == MemoryState.deleted.value:
        memory.deleted_at = datetime.now(UTC)

    # Record state change
    history = MemoryStatusHistory(
        memory_id=memory_id,
        changed_by=user_id,
        old_state=old_state.value if hasattr(old_state, 'value') else old_state,
        new_state=new_state.value if hasattr(new_state, 'value') else new_state
    )
    db.add(history)
    db.commit()
    return memory


def get_accessible_memory_ids(db: Session, app_id: UUID) -> Set[UUID]:
    """
    Get the set of memory IDs that the app has access to based on app-level ACL rules.
    Returns all memory IDs if no specific restrictions are found.
    """
    # Get app-level access controls
    app_access = db.query(AccessControl).filter(
        AccessControl.subject_type == "app",
        AccessControl.subject_id == app_id,
        AccessControl.object_type == "memory"
    ).all()

    # If no app-level rules exist, return None to indicate all memories are accessible
    if not app_access:
        return None

    # Initialize sets for allowed and denied memory IDs
    allowed_memory_ids = set()
    denied_memory_ids = set()

    # Process app-level rules
    for rule in app_access:
        if rule.effect == "allow":
            if rule.object_id:  # Specific memory access
                allowed_memory_ids.add(rule.object_id)
            else:  # All memories access
                return None  # All memories allowed
        elif rule.effect == "deny":
            if rule.object_id:  # Specific memory denied
                denied_memory_ids.add(rule.object_id)
            else:  # All memories denied
                return set()  # No memories accessible

    # Remove denied memories from allowed set
    if allowed_memory_ids:
        allowed_memory_ids -= denied_memory_ids

    return allowed_memory_ids


# List all memories with filtering
@router.get("/", response_model=Page[MemoryResponse])
async def list_memories(
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    app_id: Optional[UUID] = None,
    from_date: Optional[int] = Query(
        None,
        description="Filter memories created after this date (timestamp)",
        examples=[1718505600]
    ),
    to_date: Optional[int] = Query(
        None,
        description="Filter memories created before this date (timestamp)",
        examples=[1718505600]
    ),
    categories: Optional[str] = None,
    params: Params = Depends(),
    search_query: Optional[str] = None,
    sort_column: Optional[str] = Query(None, description="Column to sort by (memory, categories, app_name, created_at)"),
    sort_direction: Optional[str] = Query(None, description="Sort direction (asc or desc)"),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.user_id == USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Build base query - get memories with their categories as strings
    base_query = db.query(Memory).filter(
        Memory.user_id == user.id,
        Memory.state != MemoryState.deleted.value,
        Memory.state != MemoryState.archived.value,
        Memory.content.ilike(f"%{search_query}%") if search_query else True
    )

    # Apply filters
    if app_id:
        base_query = base_query.filter(Memory.app_id == app_id)

    if from_date:
        from_datetime = datetime.fromtimestamp(from_date, tz=UTC)
        base_query = base_query.filter(Memory.created_at >= from_datetime)

    if to_date:
        to_datetime = datetime.fromtimestamp(to_date, tz=UTC)
        base_query = base_query.filter(Memory.created_at <= to_datetime)

    # Add joins for app
    base_query = base_query.join(App, Memory.app_id == App.id)

    # Apply category filter if provided
    if categories:
        category_list = [c.strip() for c in categories.split(",")]
        base_query = base_query.join(Memory.categories).filter(Category.name.in_(category_list))

    # Apply sorting if specified, otherwise default to created_at DESC
    if sort_column:
        sort_field = getattr(Memory, sort_column, None)
        if sort_field:
            base_query = base_query.order_by(sort_field.desc()) if sort_direction == "desc" else base_query.order_by(sort_field.asc())
    else:
        # Default sort: newest memories first
        base_query = base_query.order_by(Memory.created_at.desc())

    # Get all results first, then manually create MemoryResponse objects to avoid pagination issues
    all_memories = base_query.all()
    
    # Filter results based on permissions and convert to MemoryResponse
    filtered_items = []
    for memory in all_memories:
        if check_memory_access_permissions(db, memory, app_id):
            # Get categories for this memory
            memory_cats = db.query(Category.name).join(memory_categories).filter(
                memory_categories.c.memory_id == memory.id
            ).all()
            category_names = [cat[0] for cat in memory_cats]
            
            # Convert SQLAlchemy Memory object to MemoryResponse
            memory_response = MemoryResponse(
                id=memory.id,
                content=memory.content,
                created_at=int(memory.created_at.timestamp()),
                state=memory.state.value if hasattr(memory.state, 'value') else str(memory.state),
                app_id=memory.app_id,
                app_name=memory.app.name if memory.app else "Unknown",
                categories=category_names,
                metadata_=memory.metadata_
            )
            filtered_items.append(memory_response)

    # Manual pagination
    total = len(filtered_items)
    start = (params.page - 1) * params.size
    end = start + params.size
    paginated_items = filtered_items[start:end]
    
    # Create pagination result manually
    from fastapi_pagination import Page
    paginated_results = Page[MemoryResponse](
        items=paginated_items,
        total=total,
        page=params.page,
        size=params.size,
        pages=(total + params.size - 1) // params.size
    )

    return paginated_results


# Get all categories
@router.get("/categories")
async def get_categories(
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.user_id == USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get unique categories associated with the user's memories
    # Get all memories
    memories = db.query(Memory).filter(Memory.user_id == user.id, Memory.state != MemoryState.deleted.value, Memory.state != MemoryState.archived.value).all()
    # Get all categories from memories
    categories = [category for memory in memories for category in memory.categories]
    # Get unique categories
    unique_categories = list(set(categories))

    return {
        "categories": unique_categories,
        "total": len(unique_categories)
    }


class CreateMemoryRequest(BaseModel):
    text: str
    metadata: dict = {}
    infer: bool = True
    app: str = "openmemory"


# Create new memory
@router.post("/")
async def create_memory(
    request: CreateMemoryRequest,
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.user_id == USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    # Get or create app
    app_obj = db.query(App).filter(App.name == request.app, App.owner_id == user.id).first()
    if not app_obj:
        app_obj = App(name=request.app, owner_id=user.id)
        db.add(app_obj)
        db.commit()
        db.refresh(app_obj)

    # Check if app is active
    if not app_obj.is_active:
        raise HTTPException(status_code=403, detail=f"App {request.app} is currently paused on OpenMemory. Cannot create new memories.")

    # Try to get memory client safely first
    try:
        memory_client = get_memory_client()
        if not memory_client:
            raise Exception("Memory client is not available")
    except Exception as client_error:
        logging.warning(f"Memory client unavailable: {client_error}. Creating memory in database only.")
        # Return a json response with the error
        return {
            "error": str(client_error)
        }

    # Check text length and determine if chunking is needed
    max_length = get_max_text_length_from_config()
    needs_chunking = len(request.text) > max_length

    if needs_chunking:
        logging.warning(f"REST API CHUNKING: Text length {len(request.text)} exceeds limit {max_length}, will auto-chunk")

        # Chunk the text
        chunks = chunk_text(request.text, max_length)
        logging.info(f"REST API CHUNKING: Created {len(chunks)} chunks from original text")

        # Process each chunk individually
        created_memories = []

        for i, chunk in enumerate(chunks):
            logging.info(f"REST API CHUNKING: Processing chunk {i+1}/{len(chunks)} (length: {len(chunk)})")

            try:
                chunk_response = memory_client.add(
                    f"[Part {i+1}/{len(chunks)}] {chunk}",
                    user_id=USER_ID,
                    metadata={
                        "source_app": "openmemory",
                        "mcp_client": request.app,
                        "chunk_info": {"chunk_number": i+1, "total_chunks": len(chunks), "original_length": len(request.text)}
                    }
                )
                
                # Process chunk response similar to normal processing
                if isinstance(chunk_response, dict) and 'results' in chunk_response:
                    if not chunk_response['results']:
                        logging.error(f"REST API CHUNKING: Chunk {i+1} failed - empty results")
                        raise HTTPException(status_code=500, detail=f"Failed to create memory chunk {i+1}/{len(chunks)}")
                    
                    for result in chunk_response['results']:
                        if result['event'] == 'ADD':
                            memory_id = UUID(result['id'])
                            
                            # Create memory in database
                            memory = Memory(
                                id=memory_id,
                                user_id=user.id,
                                app_id=app_obj.id,
                                content=result['memory'],
                                metadata_=request.metadata,
                                state=MemoryState.active.value
                            )
                            db.add(memory)
                            
                            # Create history entry
                            history = MemoryStatusHistory(
                                memory_id=memory_id,
                                changed_by=user.id,
                                old_state=MemoryState.deleted.value,
                                new_state=MemoryState.active.value
                            )
                            db.add(history)
                            
                            created_memories.append(memory)
                            logging.info(f"REST API CHUNKING: Chunk {i+1} processed successfully")
                
            except Exception as chunk_error:
                logging.error(f"REST API CHUNKING: Error processing chunk {i+1}: {chunk_error}")
                raise HTTPException(status_code=500, detail=f"Failed to process chunk {i+1}/{len(chunks)}: {str(chunk_error)}")
        
        # Commit all chunks
        db.commit()
        for memory in created_memories:
            db.refresh(memory)
        
        logging.info(f"REST API CHUNKING: Successfully processed {len(created_memories)} chunks")
        return {
            "message": f"Successfully chunked and stored {len(created_memories)} memory pieces from {len(request.text)} character text",
            "chunks_created": len(created_memories),
            "original_length": len(request.text),
            "memories": [{"id": str(mem.id), "content": mem.content[:100] + "..." if len(mem.content) > 100 else mem.content} for mem in created_memories]
        }
    else:
        logging.info(f"REST API: Text length {len(request.text)} is within limit {max_length}, processing normally")

    # Log what we're about to do
    logging.info(f"Creating memory for user_id: {USER_ID} with app: {request.app}")

    # Save to Qdrant via memory_client
    qdrant_response = memory_client.add(
        request.text,
        user_id=USER_ID,  # Use string user_id to match search
        metadata={
            "source_app": "openmemory",
            "mcp_client": request.app,
        }
    )
    
    # Log the response for debugging
    logging.info(f"Qdrant response: {qdrant_response}")
    
    # Process Qdrant response
    if isinstance(qdrant_response, dict) and 'results' in qdrant_response:
        if not qdrant_response['results']:
            # Empty results means memory was not stored
            logging.warning(f"mem0 returned empty results, likely classified as non-memorable")
            return JSONResponse(status_code=422, content={"detail": "Memory not stored: content classified as non-memorable by mem0."})
        
        for result in qdrant_response['results']:
            logging.info(f"Processing result: {result}")
            # Handle missing 'event' field gracefully (for degraded mode responses)
            event_type = result.get('event', 'ADD')  # Default to 'ADD' if missing
            is_fallback_mode = result.get('fallback_mode', False)

            if event_type == 'ADD':
                # Get the Qdrant-generated ID
                memory_id = UUID(result['id'])

                # Check if memory already exists
                logging.info(f"Checking if memory {memory_id} already exists...")
                existing_memory = db.query(Memory).filter(Memory.id == memory_id).first()
                logging.info(f"Memory exists check completed: {existing_memory is not None}")
                
                if existing_memory:
                    # Update existing memory
                    existing_memory.state = MemoryState.active.value
                    existing_memory.content = result['memory']
                    memory = existing_memory
                else:
                    # Create memory with the EXACT SAME ID from Qdrant
                    memory = Memory(
                        id=memory_id,  # Use the same ID that Qdrant generated
                        user_id=user.id,
                        app_id=app_obj.id,
                        content=result['memory'],
                        metadata_=request.metadata,
                        state=MemoryState.active.value
                    )
                    db.add(memory)
                
                # Create history entry
                history = MemoryStatusHistory(
                    memory_id=memory_id,
                    changed_by=user.id,
                    old_state=MemoryState.deleted.value if existing_memory else MemoryState.deleted.value,
                    new_state=MemoryState.active.value
                )
                db.add(history)
                
                db.commit()
                db.refresh(memory)
                return memory
            elif result.get('event') == 'NONE':
                # Handle cases where mem0 decides not to store the memory
                logging.warning(f"mem0 classified memory as non-memorable: {result}")
                logging.info(f"Returning 422 response for NONE event")
                return JSONResponse(status_code=422, content={"detail": "Memory not stored: mem0 classified content as non-memorable."})

        # If we reach here, no valid event (ADD/NONE) was found
        # Handle degraded mode responses gracefully
        if any(result.get('fallback_mode', False) for result in qdrant_response.get('results', [])):
            logging.info("Memory stored in degraded mode - treating as successful ADD operation")
            # Process as ADD event for degraded mode
            for result in qdrant_response['results']:
                if result.get('fallback_mode', False):
                    # This is already handled above in the ADD section
                    pass
        else:
            logging.error(f"No valid event (ADD/NONE) found in Qdrant response: {qdrant_response}")
            raise HTTPException(status_code=500, detail="Memory creation failed - no valid event received from mem0")


# Get memory by ID
@router.get("/{memory_id}")
async def get_memory(
    memory_id: UUID,
    db: Session = Depends(get_db)
):
    memory = get_memory_or_404(db, memory_id)

    # Create access log entry for this memory retrieval
    try:
        # Get the openmemory app for logging
        user = db.query(User).filter(User.user_id == USER_ID).first()
        logging.info(f"Access log: Found user: {user is not None}")
        if user:
            app = db.query(App).filter(App.name == "openmemory", App.owner_id == user.id).first()
            logging.info(f"Access log: Found app: {app is not None}, app_id: {app.id if app else None}")
            if app:
                access_log = MemoryAccessLog(
                    memory_id=memory_id,
                    app_id=app.id,
                    access_type="view",
                    metadata_={"source": "web_ui"}
                )
                logging.info(f"Access log: Created access log object with memory_id={memory_id}, app_id={app.id}")
                db.add(access_log)
                logging.info(f"Access log: Added to session, committing...")
                db.commit()
                logging.info(f"Access log: Committed successfully")

                # Verify the record was created
                verification = db.query(MemoryAccessLog).filter(
                    MemoryAccessLog.memory_id == memory_id,
                    MemoryAccessLog.app_id == app.id
                ).order_by(MemoryAccessLog.accessed_at.desc()).first()
                logging.info(f"Access log: Verification query result: {verification is not None}")
                if verification:
                    logging.info(f"Access log: Found record with id={verification.id}, accessed_at={verification.accessed_at}")
            else:
                logging.warning(f"Access log: No openmemory app found for user {USER_ID}")
        else:
            logging.warning(f"Access log: No user found with ID {USER_ID}")
    except Exception as e:
        # Don't fail the request if logging fails
        logging.warning(f"Failed to create access log: {e}")
        import traceback
        logging.warning(f"Access log traceback: {traceback.format_exc()}")

    return {
        "id": memory.id,
        "text": memory.content,
        "created_at": int(memory.created_at.timestamp()),
        "state": memory.state.value if hasattr(memory.state, 'value') else str(memory.state),
        "app_id": memory.app_id,
        "app_name": memory.app.name if memory.app else None,
        "categories": [category.name for category in memory.categories],
        "metadata_": memory.metadata_
    }


class DeleteMemoriesRequest(BaseModel):
    memory_ids: List[UUID]

# Delete multiple memories
@router.delete("/")
async def delete_memories(
    request: DeleteMemoriesRequest,
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.user_id == USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    logging.info(f"Proceeding with bulk deletion of {len(request.memory_ids)} memories by user {USER_ID}")
    
    for memory_id in request.memory_ids:
        update_memory_state(db, memory_id, MemoryState.deleted, user.id)
    
    return {"message": f"Successfully deleted {len(request.memory_ids)} memories"}


# Archive memories
@router.post("/actions/archive")
async def archive_memories(
    memory_ids: List[UUID],
    user_id: UUID,
    db: Session = Depends(get_db)
):
    for memory_id in memory_ids:
        update_memory_state(db, memory_id, MemoryState.archived, user_id)
    return {"message": f"Successfully archived {len(memory_ids)} memories"}


class PauseMemoriesRequest(BaseModel):
    memory_ids: Optional[List[UUID]] = None
    category_ids: Optional[List[UUID]] = None
    app_id: Optional[UUID] = None
    all_for_app: bool = False
    global_pause: bool = False
    state: Optional[MemoryState] = None

# Pause access to memories
@router.post("/actions/pause")
async def pause_memories(
    request: PauseMemoriesRequest,
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    
    global_pause = request.global_pause
    all_for_app = request.all_for_app
    app_id = request.app_id
    memory_ids = request.memory_ids
    category_ids = request.category_ids
    state = request.state
    
    user = db.query(User).filter(User.user_id == USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if global_pause:
        # Pause all memories for the user
        memories = db.query(Memory).filter(Memory.user_id == user.id).all()
        for memory in memories:
            update_memory_state(db, memory.id, state, user.id)
        return {"message": f"Successfully paused all memories for user {USER_ID}"}
    
    if all_for_app:
        # Pause all memories for a specific app
        if not app_id:
            raise HTTPException(status_code=400, detail="App ID is required to pause all memories for an app")
        
        memories = db.query(Memory).filter(Memory.app_id == app_id).all()
        for memory in memories:
            update_memory_state(db, memory.id, state, user.id)
        return {"message": f"Successfully paused all memories for app {app_id}"}
    
    if memory_ids:
        # Pause specific memories
        for memory_id in memory_ids:
            update_memory_state(db, memory_id, state, user.id)
        return {"message": f"Successfully paused {len(memory_ids)} memories"}
    
    if category_ids:
        # Pause memories by category
        memories = db.query(Memory).join(Memory.categories).filter(Category.id.in_(category_ids)).all()
        for memory in memories:
            update_memory_state(db, memory.id, state, user.id)
        return {"message": f"Successfully paused memories in {len(category_ids)} categories"}
    
    raise HTTPException(status_code=400, detail="No action specified")


# Get memory access log
@router.get("/{memory_id}/access-log")
async def get_memory_access_log(
    memory_id: UUID,
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    memory = get_memory_or_404(db, memory_id)

    # Query access logs with app information
    access_logs_query = db.query(
        MemoryAccessLog.id,
        MemoryAccessLog.memory_id,
        MemoryAccessLog.app_id,
        MemoryAccessLog.accessed_at,
        MemoryAccessLog.access_type,
        MemoryAccessLog.metadata_,
        App.name.label('app_name')
    ).join(
        App, MemoryAccessLog.app_id == App.id
    ).filter(
        MemoryAccessLog.memory_id == memory_id
    ).order_by(
        MemoryAccessLog.accessed_at.desc()
    )

    access_logs = access_logs_query.all()

    # Convert to list of dictionaries with proper structure
    result = []
    for log in access_logs:
        result.append({
            "id": str(log.id),
            "memory_id": str(log.memory_id),
            "app_id": str(log.app_id),
            "accessed_at": log.accessed_at.isoformat() if log.accessed_at else None,
            "access_type": log.access_type,
            "metadata_": log.metadata_,
            "app_name": log.app_name
        })

    return result


class FilterMemoriesRequest(BaseModel):
    app_id: Optional[UUID] = None
    from_date: Optional[int] = None
    to_date: Optional[int] = None
    categories: Optional[str] = None
    search_query: Optional[str] = None
    sort_column: Optional[str] = None
    sort_direction: Optional[str] = None

# Filter memories
@router.post("/filter", response_model=Page[MemoryResponse])
async def filter_memories(
    request: FilterMemoriesRequest,
    params: Params = Depends(),
    auth_user: Union[AuthenticatedUser, DefaultUser] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.user_id == USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Build base query
    base_query = db.query(Memory).filter(
        Memory.user_id == user.id,
        Memory.state != MemoryState.deleted.value,
        Memory.state != MemoryState.archived.value
    )

    # Apply filters from request body
    if request.app_id:
        base_query = base_query.filter(Memory.app_id == request.app_id)
    if request.from_date:
        from_datetime = datetime.fromtimestamp(request.from_date, tz=UTC)
        base_query = base_query.filter(Memory.created_at >= from_datetime)
    if request.to_date:
        to_datetime = datetime.fromtimestamp(request.to_date, tz=UTC)
        base_query = base_query.filter(Memory.created_at <= to_datetime)
    if request.search_query:
        base_query = base_query.filter(Memory.content.ilike(f"%{request.search_query}%"))

    # Join with App for sorting by app_name
    base_query = base_query.join(App, Memory.app_id == App.id)

    # Apply category filter
    if request.categories:
        category_list = [c.strip() for c in request.categories.split(",")]
        base_query = base_query.join(Memory.categories).filter(Category.name.in_(category_list))

    # Apply sorting, default to created_at DESC if no sort specified
    if request.sort_column:
        sort_field = None
        if request.sort_column == "app_name":
            sort_field = App.name
        else:
            sort_field = getattr(Memory, request.sort_column, None)

        if sort_field:
            base_query = base_query.order_by(sort_field.desc()) if request.sort_direction == "desc" else base_query.order_by(sort_field.asc())
    else:
        # Default sort: newest memories first
        base_query = base_query.order_by(Memory.created_at.desc())

    # Apply pagination at the database level for better performance
    total = base_query.count()
    start = (params.page - 1) * params.size
    paginated_memories = base_query.offset(start).limit(params.size).all()

    # Convert to MemoryResponse objects with proper category handling
    filtered_items = []
    for memory in paginated_memories:
        # Skip permission check for now to debug
        # if check_memory_access_permissions(db, memory, request.app_id):

        # Properly fetch categories for this memory
        memory_cats = db.query(Category.name).join(memory_categories).filter(
            memory_categories.c.memory_id == memory.id
        ).all()
        category_names = [cat[0] for cat in memory_cats]

        # Convert SQLAlchemy Memory object to MemoryResponse
        memory_response = MemoryResponse(
            id=memory.id,
            content=memory.content,
            created_at=int(memory.created_at.timestamp()),
            state=memory.state.value if hasattr(memory.state, 'value') else str(memory.state),
            app_id=memory.app_id,
            app_name=memory.app.name if memory.app else "Unknown",
            categories=category_names,
            metadata_=memory.metadata_
        )
        filtered_items.append(memory_response)

    # Create pagination result
    from fastapi_pagination import Page
    paginated_results = Page[MemoryResponse](
        items=filtered_items,
        total=total,
        page=params.page,
        size=params.size,
        pages=(total + params.size - 1) // params.size
    )

    return paginated_results


@router.get("/{memory_id}/related", response_model=Page[MemoryResponse])
async def get_related_memories(
    memory_id: UUID,
    user_id: str = Query(..., description="User ID to filter memories"),
    params: Params = Depends(),
    db: Session = Depends(get_db)
):
    """Get related memories based on shared categories."""
    # Validate user
    user = db.query(User).filter(User.user_id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get the source memory
    memory = get_memory_or_404(db, memory_id)

    # Extract category IDs from the source memory
    category_ids = [category.id for category in memory.categories]

    if not category_ids:
        # Return empty page if no categories
        return Page.create([], total=0, params=params)

    # Build query for related memories
    # First, get memories that share categories with the source memory
    query = db.query(Memory).filter(
        Memory.user_id == user.id,
        Memory.id != memory_id,  # Exclude the source memory
        Memory.state != MemoryState.deleted.value
    ).join(Memory.categories).filter(
        Category.id.in_(category_ids)
    ).options(
        joinedload(Memory.categories),
        joinedload(Memory.app)
    ).group_by(Memory.id).order_by(
        func.count(Category.id).desc(),  # Order by number of shared categories
        Memory.created_at.desc()
    )

    # Force page size to be 5 for related memories
    params = Params(page=params.page, size=5)

    # Get paginated results
    total = query.count()
    start = (params.page - 1) * params.size
    paginated_memories = query.offset(start).limit(params.size).all()

    # Convert to MemoryResponse objects
    memory_responses = []
    for memory in paginated_memories:
        # Get categories for this memory
        memory_cats = db.query(Category.name).join(memory_categories).filter(
            memory_categories.c.memory_id == memory.id
        ).all()
        category_names = [cat[0] for cat in memory_cats]

        memory_response = MemoryResponse(
            id=memory.id,
            content=memory.content,
            created_at=int(memory.created_at.timestamp()),
            state=memory.state.value if hasattr(memory.state, 'value') else str(memory.state),
            app_id=memory.app_id,
            app_name=memory.app.name if memory.app else "Unknown",
            categories=category_names,
            metadata_=memory.metadata_
        )
        memory_responses.append(memory_response)

    # Create pagination result
    return Page[MemoryResponse](
        items=memory_responses,
        total=total,
        page=params.page,
        size=params.size,
        pages=(total + params.size - 1) // params.size
    )